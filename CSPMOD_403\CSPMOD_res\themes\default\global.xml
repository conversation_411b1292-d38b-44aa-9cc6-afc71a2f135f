<?xml version="1.0" encoding="UTF-8"?>
<Global>
    <!--默认字体名称，列表形式，依次匹配，直到发现第一个有效字体作为默认字体名称，多个字体名称用逗号分隔-->
    <DefaultFontFamilyNames value="微软雅黑,宋体,PingFang SC"/>
    
    <!--所有字体：默认为五号字-->
    <Font id="system_12" name="system" size="12"/>
    <Font id="system_14" name="system" size="14" default="true"/>
    <Font id="system_16" name="system" size="16"/>
    <Font id="system_18" name="system" size="18"/>
    <Font id="system_20" name="system" size="20"/>
    <Font id="system_22" name="system" size="22"/>
    <Font id="system_bold_12" name="system" size="12" bold="true"/>
    <Font id="system_bold_14" name="system" size="14" bold="true"/>
    <Font id="system_bold_16" name="system" size="16" bold="true"/>
    <Font id="system_bold_18" name="system" size="18" bold="true"/>
    <Font id="system_bold_20" name="system" size="20" bold="true"/>
    <Font id="system_bold_22" name="system" size="22" bold="true"/>
    <Font id="system_underline_12" name="system" size="12" underline="true"/>
    <Font id="system_underline_14" name="system" size="14" underline="true"/>
    <Font id="system_underline_16" name="system" size="16" underline="true"/>
    <Font id="system_underline_18" name="system" size="18" underline="true"/>
    <Font id="system_underline_20" name="system" size="20" underline="true"/>
    <Font id="system_underline_22" name="system" size="22" underline="true"/>
    <Font id="system_italic_12" name="system" size="12" italic="true"/>
    <Font id="system_italic_14" name="system" size="14" italic="true"/>
    <Font id="system_italic_16" name="system" size="16" italic="true"/>
    <Font id="system_italic_18" name="system" size="18" italic="true"/>
    <Font id="system_italic_20" name="system" size="20" italic="true"/>
    <Font id="system_italic_22" name="system" size="22" italic="true"/>
    <Font id="system_strikeout_12" name="system" size="12" strikeout="true"/>
    <Font id="system_strikeout_14" name="system" size="14" strikeout="true"/>
    <Font id="system_strikeout_16" name="system" size="16" strikeout="true"/>
    <Font id="system_strikeout_18" name="system" size="18" strikeout="true"/>
    <Font id="system_strikeout_20" name="system" size="20" strikeout="true"/>
    <Font id="system_strikeout_22" name="system" size="22" strikeout="true"/>
    <Font id="system_fullstyle_20" name="system" size="20" italic="true" strikeout="true" underline="true" bold="true"/>
    <Font id="arial_12" name="arial" size="12"/>
    <Font id="arial_14" name="arial" size="14"/>
    <Font id="arial_16" name="arial" size="16"/>
    <Font id="arial_18" name="arial" size="18"/>
    <Font id="arial_20" name="arial" size="20"/>
    <Font id="arial_22" name="arial" size="22"/>
    <Font id="arial_bold_12" name="arial" size="12" bold="true"/>
    <Font id="arial_bold_14" name="arial" size="14" bold="true"/>
    <Font id="arial_bold_16" name="arial" size="16" bold="true"/>
    <Font id="arial_bold_18" name="arial" size="18" bold="true"/>
    <Font id="arial_bold_20" name="arial" size="20" bold="true"/>
    <Font id="arial_bold_22" name="arial" size="22" bold="true"/>
    
    <!-- 字体文件（放在资源根目录的fonts目录中），程序启动时加载，加载后可以按照使用系统字体相同的方式使用 -->
    <!-- 注意事项：该RobotoMono字体只能用于显示英文字母，不支持中文 -->
    <FontFile file="RobotoMono-Regular.ttf" desc="字体名称：Roboto Mono，常规字体"/>
    <FontFile file="RobotoMono-Bold.ttf" desc="字体名称：Roboto Mono，粗体"/>
    <FontFile file="RobotoMono-Italic.ttf" desc="字体名称：Roboto Mono，斜体"/>
    <FontFile file="RobotoMono-BoldItalic.ttf" desc="字体名称：Roboto Mono，粗斜体"/>
    
    <!-- 默认文本颜色值：常规状态 -->
    <TextColor name="default_font_color" value="#FF000000"/>
    <!-- 默认文本颜色值：禁止状态 -->
    <TextColor name="disabled_font_color" value="#FFA1AEBC"/>
    
    <!-- 超级链接：常规文本颜色值 -->
    <TextColor name="default_link_font_color" value="#FF0000FF"/>
    <!-- 超级链接：Hover状态文本颜色值 -->
    <TextColor name="hover_link_font_color" value="#FFD3215F"/>
    <!-- 超级链接：鼠标按下状态文本颜色值 -->
    <TextColor name="mouse_down_link_font_color" value="#FFFF0000"/>
    
    <!--一些常规固定颜色-->
    <TextColor name="white" value="#ffffffff"/>
    <TextColor name="black" value="#ff000000"/>
    <TextColor name="darkcolor" value="#ff333333"/>
    <TextColor name="lightcolor" value="#ff888888"/>
    <TextColor name="gray" value="#ff8e99a6"/>
    <TextColor name="light_gray" value="#ffa8a8a8"/>
    <TextColor name="dark_gray" value="#ff72797f"/>
    <TextColor name="green" value="#ff00bb96"/>
    <TextColor name="light_green" value="#ff21c7a6"/>
    <TextColor name="blue" value="#ff006DD9"/>
    <TextColor name="red" value="#ffC63535"/>
    
    <!--窗口常用背景色-->
    <TextColor name="bk_main_wnd_title" value="#ff238efa"/>
    <TextColor name="bk_main_wnd_search" value="#ff1e7ad7"/>
    <TextColor name="bk_wnd_darkcolor" value="#fff0f2f5"/>
    <TextColor name="bk_wnd_lightcolor" value="#ffffffff"/>
    <TextColor name="bk_menuitem_hovered" value="#ffe1e6eb"/>
    <TextColor name="bk_menuitem_selected" value="#ffced4db"/>
    <TextColor name="bk_listitem_hovered" value="#fff0f2f5"/>
    <TextColor name="bk_listitem_selected" value="#ffe4e7eb"/>
    <!--置顶项的背景颜色-->
    <TextColor name="bk_topitem_normal" value="#ffffffe0"/>
    <TextColor name="bk_topitem_hovered" value="#ffffffeb"/>
    <TextColor name="bk_topitem_selected" value="#fffafacd"/>
    <!--进度条专用-->
    <TextColor name="bk_progress_bk" value="#ffe3ecf5"/>
    <TextColor name="bk_progress_progress" value="#ff2aceae"/>
    <!--背景色值，半透明-->
    <TextColor name="transbkblack" value="#80000000"/>
    <TextColor name="maskbkblack" value="#81000000"/>
    <!--第一级分割线，较深-->
    <TextColor name="splitline_level1" value="#ffd2d4d6"/>
    <!--第二级分割线，较浅-->
    <TextColor name="splitline_level2" value="#ffebedf0"/>
    <!--调色板色值-->
    <TextColor name="color_palette1" value="#ffff2600"/>
    <TextColor name="color_palette2" value="#ffffeb00"/>
    <TextColor name="color_palette3" value="#ff61ed00"/>
    <TextColor name="color_palette4" value="#ff00b4ff"/>
    <TextColor name="color_palette5" value="#ffa100ff"/>
    <TextColor name="color_palette6" value="#ff000000"/>
    <TextColor name="color_palette7" value="#ff545454"/>
    <TextColor name="color_palette8" value="#ffa8a8a8"/>
    <TextColor name="color_palette9" value="#ffffffff"/>
    <!--duilib控件相关颜色-->
    <TextColor name="grid_line" value="#ffcccccc"/>
    <TextColor name="grid_sel_fore" value="#ff20b0fb"/>
    
    <TextColor name="richedit_border_color" value="#AAA8A8A8"/>
    <TextColor name="richedit_hot_border_color" value="CornflowerBlue"/>
    <TextColor name="richedit_focus_border_color" value="#FF8591A2"/>
    <TextColor name="richedit_focus_bottom_border_color" value="#FF1D6978"/>

    <!--字体大小-->
    <Class name="font_title" font="system_14"/>
    <!--滚动条: 简约风格，为默认滚动条样式，不可删除-->
    <Class name="vscrollbar" width="12" fade_alpha="true" 
                             thumb_normal_image="file='public/scrollbar01/vscrollbar/thumb_normal.svg' width='12' corner='0,8,0,8'" 
                             thumb_hot_image="file='public/scrollbar01/vscrollbar/thumb_hovered.svg' width='12' corner='0,8,0,8'" 
                             bk_hot_image="file='public/scrollbar01/vscrollbar/bk_hovered.svg' width='12' corner='0,8,0,8'" 
                             show_button1="false" 
                             show_button2="false" />
    <Class name="hscrollbar" height="12" fade_alpha="true" 
                             thumb_normal_image="file='public/scrollbar01/hscrollbar/thumb_normal.svg' height='12' corner='8,0,8,0'" 
                             thumb_hot_image="file='public/scrollbar01/hscrollbar/thumb_hovered.svg' height='12' corner='8,0,8,0'" 
                             bk_hot_image="file='public/scrollbar01/hscrollbar/bk_hovered.svg' height='12' corner='8,0,8,0'" 
                             show_button1="false" 
                             show_button2="false"/>
    <!--滚动条: 标准风格-->
    <Class name="vscrollbar2" width="12" fade_alpha="true"
                             thumb_normal_image="file='public/scrollbar02/vscrollbar/thumb_normal.svg' width='12' corner='0,8,0,8'" 
                             thumb_hot_image="file='public/scrollbar02/vscrollbar/thumb_hovered.svg' width='12' corner='0,8,0,8'" 
                             bk_normal_image="file='public/scrollbar02/vscrollbar/bk_normal.svg' width='12' corner='0,8,0,8'" 
                             bk_hot_image="file='public/scrollbar02/vscrollbar/bk_hovered.svg' width='12' corner='0,8,0,8'"                              
                             show_button1="true" 
                             button1_normal_image="file='public/scrollbar02/vscrollbar/btn_top_normal.svg' width='12' "
                             button1_hot_image="file='public/scrollbar02/vscrollbar/btn_top_hover.svg' width='12' "
                             button1_pushed_image="file='public/scrollbar02/vscrollbar/btn_top_pushed.svg' width='12' "
                             button1_disabled_image="file='public/scrollbar02/vscrollbar/btn_top_disabled.svg' width='12' "
                             show_button2="true"
                             button2_normal_image="file='public/scrollbar02/vscrollbar/btn_bottom_normal.svg' width='12' "
                             button2_hot_image="file='public/scrollbar02/vscrollbar/btn_bottom_hover.svg' width='12' "
                             button2_pushed_image="file='public/scrollbar02/vscrollbar/btn_bottom_pushed.svg' width='12' "
                             button2_disabled_image="file='public/scrollbar02/vscrollbar/btn_bottom_disabled.svg' width='12' " />
    <Class name="hscrollbar2" height="12" fade_alpha="true" 
                             thumb_normal_image="file='public/scrollbar02/hscrollbar/thumb_normal.svg' height='12' corner='8,0,8,0'" 
                             thumb_hot_image="file='public/scrollbar02/hscrollbar/thumb_hovered.svg' height='12' corner='8,0,8,0'" 
                             bk_normal_image="file='public/scrollbar02/hscrollbar/bk_normal.svg' height='12' corner='8,0,8,0'" 
                             bk_hot_image="file='public/scrollbar02/hscrollbar/bk_hovered.svg' height='12' corner='8,0,8,0'" 
                             show_button1="true" 
                             button1_normal_image="file='public/scrollbar02/hscrollbar/btn_left_normal.svg' height='12' "
                             button1_hot_image="file='public/scrollbar02/hscrollbar/btn_left_hover.svg' height='12' "
                             button1_pushed_image="file='public/scrollbar02/hscrollbar/btn_left_pushed.svg' height='12' "
                             button1_disabled_image="file='public/scrollbar02/hscrollbar/btn_left_disabled.svg' height='12' "
                             show_button2="true"
                             button2_normal_image="file='public/scrollbar02/hscrollbar/btn_right_normal.svg' height='12' "
                             button2_hot_image="file='public/scrollbar02/hscrollbar/btn_right_hover.svg' height='12' "
                             button2_pushed_image="file='public/scrollbar02/hscrollbar/btn_right_pushed.svg' height='12' "
                             button2_disabled_image="file='public/scrollbar02/hscrollbar/btn_right_disabled.svg' height='12' " />
    <!--单选框-->
    <Class name="option_1" height="20" text_padding="22,0,0,0" text_align="vcenter" valign="center"
                           normal_image="file='public/option/radio-circle-unchecked.svg' width='20' height='20' valign='center'" 
                           selected_normal_image="file='public/option/radio-circle-checked.svg' width='20' height='20' valign='center'"/>
    <Class name="option_2" height="18" text_padding="20,0,0,0" text_align="vcenter" valign="center"
                           normal_image="file='public/option/radio-unchecked.svg' width='16' height='16' valign='center'" 
                           selected_normal_image="file='public/option/radio-checked.svg' width='16' height='16' valign='center'"/>
    <!--复选框-->
    <Class name="checkbox_toggle_1" height="28" valign="center"
                                    normal_image="file='public/checkbox/checkbox-toggle-off.svg' width='26' height='26' valign='center'" 
                                    selected_normal_image="file='public/checkbox/checkbox-toggle-on.svg' width='26' height='26' valign='center'"/>
    <Class name="checkbox_toggle_2" height="18" width="38" valign="center"
                                    normal_image="file='public/checkbox/checkbox-toggle.svg' width='64' height='64' src='0,0,64,30'" 
                                    selected_normal_image="file='public/checkbox/checkbox-toggle.svg' width='64' height='64' src='0,34,64,64'"/>
    <Class name="checkbox_1" height="20" text_padding="20,0,0,0" text_align="vcenter" valign="center"
                             normal_image="file='public/checkbox/checkbox-fill-unchecked.svg' width='16' height='16' valign='center'" 
                             disabled_image="file='public/checkbox/checkbox-fill-unchecked.svg' width='16' height='16' valign='center' fade='80'" 
                             selected_normal_image="file='public/checkbox/checkbox-fill-checked.svg' width='16' height='16' valign='center'" 
                             selected_disabled_image="file='public/checkbox/checkbox-fill-checked.svg' width='16' height='16' valign='center' fade='80'"/>
    <Class name="checkbox_2" height="20" text_padding="20,0,0,0"  text_align="vcenter" valign="center"
                             normal_image="file='public/checkbox/checkbox-outline-unchecked.svg' width='18' height='18' valign='center'" 
                             disabled_image="file='public/checkbox/checkbox-outline-unchecked.svg' width='18' height='18' valign='center' fade='80'" 
                             selected_normal_image="file='public/checkbox/checkbox-outline-checked.svg' width='18' height='18' valign='center'" 
                             selected_disabled_image="file='public/checkbox/checkbox-outline-checked.svg' width='18' height='18' valign='center' fade='80'"/>
    <!--下拉框-->
    <Class name="combo" bkcolor="white" padding="1,1,1,1" border_size="1" border_color="light_gray" hot_border_color="blue" 
                        combo_tree_view_class="padding='1,1,1,1' border_size='1,1,1,1' bkcolor='white' border_color='gray' indent='20' class='tree_view'"
                        combo_tree_node_class="tree_node" 
                        combo_icon_class="bkimage='public/caption/logo.svg' width='18' height='18' valign='center' margin='2,0,2,0'" 
                        combo_edit_class="bkcolor='white' text_align='vcenter' text_padding='2,0,2,0' single_line='true' word_wrap='false' auto_hscroll='true'"
                        combo_button_class="height={stretch} width={auto} margin={1,0,0,0} padding={1,0,0,0} border_size={1,0,0,0} valign={center} 
                                            hot_border_color={blue} 
                                            pushed_border_color={blue}                                             
                                            hot_color={#FFE5F3FF} 
                                            pushed_color={#FFCCE8FF} 
                                            normal_image={file='public/combo/arrow_normal.svg' valign='center'} 
                                            hot_image={file='public/combo/arrow_hot.svg' valign='center'}"/>
    <Class name="filter_combo" bkcolor="white" padding="1,1,1,1" border_size="1" border_color="light_gray" hot_border_color="blue" 
                               combo_tree_view_class="padding='1,1,1,1' border_size='1,1,1,1' bkcolor='white' border_color='gray' indent='20' class='tree_view'"
                               combo_tree_node_class="tree_node" 
                               combo_icon_class="bkimage='public/caption/logo.svg' width='18' height='18' valign='center' margin='2,0,2,0'" 
                               combo_edit_class="bkcolor='white' text_align='vcenter' text_padding='2,0,2,0' single_line='true' word_wrap='false' auto_hscroll='true'"/>
    <Class name="check_combo" bordersize="1" bordercolor="splitline_level1" 
                              dropbox="padding='1,1,1,1' bkcolor='bk_wnd_lightcolor' border_color='splitline_level1' border_size='1,1,1,1' vscrollbar='true'"
                              dropbox_item_class="width={stretch} height={24} text_padding={20,0,2,1} text_align={left,vcenter} 
                                                  normal_image={file='public/checkbox/checkbox-outline-unchecked.svg' valign='center'} 
                                                  selected_normal_image={file='public/checkbox/checkbox-outline-checked.svg' valign='center'}" 
                              selected_item_class="width={auto} height={22} margin={4,2,4,2} bkcolor={bk_menuitem_selected} text_padding={2,1,2,1}"/>
    <Class name="combo_button" padding="1,1,1,1" border_size="1" hot_border_color="blue" hot_color="#FFE5F3FF" pushed_color="#FFCCE8FF"
                               combo_box_class="padding='1,1,1,1' border_size='1,1,1,1' bkcolor='white' border_color='gray'" 
                               left_button_class="height={stretch} width={stretch} padding={0,0,1,0} border_size={0,0,1,0} valign={center} 
                                                  hot_border_color={blue} 
                                                  pushed_border_color={blue}                                                   
                                                  hot_color={#FFE5F3FF} 
                                                  pushed_color={#FFCCE8FF} " 
                               left_button_top_label_class="width={stretch} height={20} margin={1,0,1,0} font={system_bold_14} text_align={bottom,hcenter}" 
                               left_button_bottom_label_class="width={stretch} height={stretch} margin={6,0,6,2} bkcolor={black}" 
                               right_button_class="height={stretch} width={auto} padding={1,0,0,0} border_size={1,0,0,0} valign={center} 
                                                   hot_border_color={blue} 
                                                   pushed_border_color={blue}                                                    
                                                   hot_color={#FFE5F3FF} 
                                                   pushed_color={#FFCCE8FF} 
                                                   normal_image={file='public/combo/arrow_normal.svg' valign='center'} 
                                                   hot_image={file='public/combo/arrow_hot.svg' valign='center'}"/>

    <!--按钮：图片按钮-->
    <Class name="btn_global_blue_80x30" font="system_bold_14" normal_text_color="white" 
                                        normal_image="file='public/button/btn_global_blue_80x30_normal.png'" 
                                        hot_image="file='public/button/btn_global_blue_80x30_hovered.png'" 
                                        pushed_image="file='public/button/btn_global_blue_80x30_pushed.png'" 
                                        disabled_image="file='public/button/btn_global_blue_80x30_normal.png' fade='80'"/>
    <Class name="btn_global_white_80x30" font="system_bold_14" normal_text_color="dark_gray" 
                                         normal_image="file='public/button/btn_global_white_80x30_normal.png'" 
                                         hot_image="file='public/button/btn_global_white_80x30_hovered.png'" 
                                         pushed_image="file='public/button/btn_global_white_80x30_pushed.png'" 
                                         disabled_image="file='public/button/btn_global_white_80x30_normal.png' fade='128'"/>
    <Class name="btn_global_red_80x30" font="system_bold_14" normal_text_color="white" 
                                       normal_image="file='public/button/btn_global_red_80x30_normal.png'" 
                                       hot_image="file='public/button/btn_global_red_80x30_hovered.png'" 
                                       pushed_image="file='public/button/btn_global_red_80x30_pushed.png'" 
                                       disabled_image="file='public/button/btn_global_red_80x30_normal.png' fade='80'"/>
    <!--按钮：纯色按钮-->
    <Class name="btn_global_color_gray" font="system_14" text_align="hcenter,vcenter" 
                                        normal_text_color="black" 
                                        disabled_text_color="gray" 
                                        normal_color="#FFD7DCE0" 
                                        hot_color="#FFFAFCFF" 
                                        pushed_color="#FFB0B4B8"/>
    
    <!--窗口右上角按钮 -->
    <Class name="btn_wnd_min_11" normal_image="file='public/button/window-minimize.svg' 
                                 width='24' height='24' valign='center' halign='center'" hot_color="AliceBlue" pushed_color="Lavender"/>
    <Class name="btn_wnd_close_11" normal_image="file='public/button/window-close.svg' 
                                 width='24' height='24' valign='center' halign='center'" hot_color="red" pushed_color="OrangeRed"/>
    <Class name="btn_wnd_max_11" normal_image="file='public/button/window-maximize.svg' 
                                 width='24' height='24' valign='center' halign='center'" hot_color="AliceBlue" pushed_color="Lavender"/>
    <Class name="btn_wnd_restore_11" normal_image="file='public/button/window-restore.svg' 
                                 width='24' height='24' valign='center' halign='center'" hot_color="AliceBlue" pushed_color="Lavender"/>
    <Class name="btn_wnd_fullscreen_11" normal_image="file='public/button/window-fullscreen.svg'
                                 width='24' height='24' valign='center' halign='center' padding='0,4,0,0'" hot_color="AliceBlue" pushed_color="Lavender"/>
    <Class name="btn_wnd_settings_11" normal_image="file='public/button/btn_setting.svg' 
                                 width='20' height='20' valign='center' halign='center'" hot_color="AliceBlue" pushed_color="Lavender"/>
     
    <!--删除按钮通用样式-->
    <Class name="btn_recycle" width="auto" height="auto" 
                              normal_image="file='public/button/btn_recycle_normal.svg'" 
                              hot_image="file='public/button/btn_recycle_hovered.svg'" 
                              pushed_image="file='public/button/btn_recycle_pushed.svg'" 
                              disabled_image="file='public/button/btn_recycle_normal.svg' fade='80'"/>
    <!--列表通用样式-->
    <Class name="list" bkcolor="bk_wnd_lightcolor" vscrollbar="true"/>
    <Class name="listitem" hot_color="bk_listitem_hovered" 
                           pushed_color="bk_listitem_selected" 
                           selected_normal_color="bk_listitem_selected" 
                           fadehot="false"/>
    <Class name="list_topitem" normal_color="bk_topitem_normal" 
                               hot_color="bk_topitem_hovered" 
                               pushed_color="bk_topitem_selected" 
                               selected_normal_color="bk_topitem_selected" 
                               fadehot="false"/>
    <!--TreeView通用样式-->
    <Class name="tree_view" bkcolor="bk_wnd_lightcolor" vscrollbar="true"/>
    <Class name="tree_node" height="28" hot_color="bk_listitem_hovered" 
                                        pushed_color="bk_listitem_selected" 
                                        selected_normal_color="bk_listitem_selected" 
                                        icon_image_right_space="4" 
                                        fadehot="false"/>
    <Class name="tree_node_checkbox" normal_image="file='public/checkbox/checkbox-outline-unchecked.svg' padding='2,0,0,0' valign='center'" 
                                     disabled_image="file='public/checkbox/checkbox-outline-unchecked.svg' padding='2,0,0,0' valign='center' fade='80'" 
                                     selected_normal_image="file='public/checkbox/checkbox-outline-checked.svg' padding='2,0,0,0' valign='center'" 
                                     selected_disabled_image="file='public/checkbox/checkbox-outline-checked.svg' padding='2,0,0,0' valign='center' fade='80'" 
                                     part_selected_normal_image="file='public/checkbox/checkbox-outline-mixed.svg' padding='2,0,0,0' valign='center'" 
                                     part_selected_disabled_image="file='public/checkbox/checkbox-outline-mixed.svg' padding='2,0,0,0' valign='center' fade='80'"
                                     check_box_image_right_space="6"/>
    <Class name="tree_node_expand" expand_normal_image="file='public/tree/expand_normal.svg' padding='2,0,0,0' valign='center'" 
                                   expand_hot_image="file='public/tree/expand_hot.svg' padding='2,0,0,0' valign='center'" 
                                   expand_disabled_image="file='public/tree/expand_normal.svg' padding='2,0,0,0' valign='center' fade='80'" 
                                   collapse_normal_image="file='public/tree/collapse_normal.svg' padding='2,0,0,0' valign='center'" 
                                   collapse_hot_image="file='public/tree/collapse_hot.svg' padding='2,0,0,0' valign='center'" 
                                   collapse_disabled_image="file='public/tree/collapse_normal.svg' padding='2,0,0,0' valign='center' fade='80'"
                                   icon_image_right_space="4"/>
    <!--进度条通用样式-->
    <Class name="progress_horizontal_blue" valign="center" horizontal="true" height="3" width="stretch" value="0" max="100" min="0" 
                                           bkimage="public/progress/progress_blue_bg.png" 
                                           progress_image="public/progress/progress_blue_fg.png"/>
    <Class name="progress_vertical_blue" valign="center" horizontal="false" width="3" height="stretch" value="0" max="100" min="0" 
                                         bkimage="public/progress/progress_blue_bg_ver.png" 
                                         progress_image="public/progress/progress_blue_fg_ver.png"/>
    <Class name="slider_horizontal_green" width="stretch" height="14" margin="4,0,0,0" valign="center" thumb_size="14,14" min="0" max="255" 
                                          progress_bar_padding="0,6,0,6" 
                                          progress_color="bk_progress_progress" 
                                          bkimage="file='public/slider/slider_hor_bk.png' valign='center' height='3' width='1200'" 
                                          thumb_normal_image="file='public/slider/slider_thumb.svg' width='14' height='14'"/>
    <Class name="slider_vertical_green"   width="stretch" height="14" margin="4,0,0,0" valign="center" thumb_size="14,14" min="0" max="255" 
                                          progress_bar_padding="0,6,0,6" 
                                          progress_color="bk_progress_progress" 
                                          bkimage="file='public/slider/slider_ver_bk.png' halign='center' width='3' height='1200'" 
                                          thumb_normal_image="file='public/slider/slider_thumb.svg' width='14' height='14'"/>
    <!--菜单通用样式-->
    <Class name="menu" width="auto" height="auto" bkcolor="bk_wnd_lightcolor"/>
    <Class name="menu_element" height="32" padding="20,0,20,0" margin="1,1,1,1" 
                                hot_color="bk_menuitem_hovered"
                                pushed_color="bk_menuitem_selected" 
                                selected_normal_color="bk_menuitem_selected"/>
    <Class name="menu_text" normal_text_color="darkcolor" font="system_14" valign="center"/>
    <Class name="menu_split_box" padding="16,0,16,0" height="auto" />
    <Class name="menu_split_line" bkcolor="splitline_level1" height="1" />
    <Class name="menu_checkbox" height="auto" valign="center" text_padding="20,0,0,0" font="system_14" 
                                normal_text_color="darkcolor" 
                                normal_image="" 
                                disabled_image="" 
                                selected_normal_image="file='public/checkbox/checkbox-outline-checked.svg' valign='center'" 
                                selected_disabled_image="file='public/checkbox/checkbox-outline-checked.svg' valign='center' fade='80'"/>
    <!--分割线通用样式-->
    <Class name="splitline_hor_level1" bkcolor="splitline_level1" height="1"/>
    <Class name="splitline_hor_level2" bkcolor="splitline_level2" height="1"/>
    <Class name="splitline_ver_level1" bkcolor="splitline_level1" width="1"/>
    
    <!--RichEdit富文本控件通用样式-->
    <Class name="simple" bkcolor="white" multi_line="false" auto_hscroll="true" rich_text="false"
                         want_return="false" want_ctrl_return="false" want_tab="false" 
                         disabled_text_color="disabled_font_color"/>
    <Class name="simple_border" padding="1,1,1,1" border_size="1" 
                                border_color="richedit_border_color" 
                                hot_border_color="richedit_hot_border_color" 
                                focus_border_color="richedit_focus_border_color"/>
    <Class name="simple_border_bottom" padding="1,1,1,1" border_size="1,1,1,1" 
                                       border_color="richedit_border_color" 
                                       hot_border_color="richedit_hot_border_color" 
                                       focus_border_color="richedit_focus_border_color"
                                       focus_bottom_border_size="2"
                                       focus_bottom_border_color="richedit_focus_bottom_border_color"/>
    <Class name="prompt" prompt_mode="true" prompt_color="light_gray"/>
    
    <!--RichEdit上的Spin按钮样式: 左侧和下侧有边线-->
    <Class name="rich_edit_spin" spin_class="rich_edit_spin_box,rich_edit_spin_btn_up,rich_edit_spin_btn_down" 
                                 bkcolor="white" number_only="true" height="28" width="64" text_padding="0, 0, 18, 0" text_align="vcenter,hcenter"/>
    <Class name="rich_edit_spin_box" width="18" halign="right" nofocus="true"/>
    <Class name="rich_edit_spin_btn_up" height="50%" width="100%" nofocus="true" padding="1,0,0,0" border_size="1,0,0,0" 
                                 border_color="light_gray" 
                                 normal_image="file='public/button/arrow-up.svg' width='10' height='10' valign='center' halign='center'" 
                                 normal_color="bk_wnd_darkcolor" hot_color="#FFE5F3FF" 
                                 pushed_color="#FFCCE8FF"/>
    <Class name="rich_edit_spin_btn_down" height="50%" width="100%" nofocus="true" padding="1,1,0,0" border_size="1,1,0,0" 
                                 border_color="light_gray"
                                 normal_image="file='public/button/arrow-down.svg' width='10' height='10' valign='center' halign='center'" 
                                 normal_color="bk_wnd_darkcolor" hot_color="#FFE5F3FF" 
                                 pushed_color="#FFCCE8FF"/>
    
    <!--RichEdit上的Spin按钮样式: 四周都有边线-->
    <Class name="rich_edit_spin2" spin_class="rich_edit_spin_box,rich_edit_spin_btn_up2,rich_edit_spin_btn_down2" 
                                  bkcolor="white" number_only="true" height="28" width="64" text_padding="0, 0, 18, 0" text_align="vcenter,hcenter"/>
    <Class name="rich_edit_spin_btn_up2" height="50%" width="100%" nofocus="true" padding="1,1,1,1" border_size="1,1,1,1" 
                                  border_color="light_gray" 
                                  normal_image="file='public/button/arrow-up.svg' width='10' height='10' valign='center' halign='center'" 
                                  normal_color="bk_wnd_darkcolor" 
                                  hot_color="#FFE5F3FF" 
                                  pushed_color="#FFCCE8FF"/>
    <Class name="rich_edit_spin_btn_down2" height="50%" width="100%" nofocus="true" padding="1,0,1,1" border_size="1,0,1,1" 
                                  border_color="light_gray" 
                                  normal_image="file='public/button/arrow-down.svg' width='10' height='10' valign='center' halign='center'" 
                                  normal_color="bk_wnd_darkcolor" 
                                  hot_color="#FFE5F3FF" 
                                  pushed_color="#FFCCE8FF"/>
    
    <!--RichEdit上的清除按钮样式-->
    <Class name="rich_edit_clear_btn" clear_btn_class="rich_edit_clear_btn_class" text_align="vcenter"/>
    <Class name="rich_edit_clear_btn_class" height="22" width="24" border_round="4,4" margin="0,0,2,0" halign="right" valign="center" 
                                            normal_image="file='public/button/window-close.svg' width='18' height='18' valign='center' halign='center'" 
                                            normal_color="bk_wnd_darkcolor" 
                                            hot_color="#FFE5F3FF" 
                                            pushed_color="#FFCCE8FF"/>

    <!--RichEdit上的显示密码按钮样式-->
    <Class name="rich_edit_show_password_btn" password="true" show_password="false" show_passowrd_btn_class="rich_edit_show_password_btn_class" text_align="vcenter"/>
    <Class name="rich_edit_show_password_btn_class" height="22" width="24" border_round="4,4" margin="0,0,2,0" halign="right" valign="center" 
                                                    normal_image="file='public/button/icon_visibility.svg' width='14' height='14' valign='center' halign='center'" 
                                                    normal_color="bk_wnd_darkcolor" 
                                                    hot_color="#FFE5F3FF" 
                                                    pushed_color="#FFCCE8FF"/>    
    
    <!--RichText 的基本属性-->
    <Class name="rich_text" text_color="default_font_color" 
                            default_link_font_color="default_link_font_color" 
                            hover_link_font_color="hover_link_font_color" 
                            mouse_down_link_font_color="mouse_down_link_font_color" 
                            link_font_underline="true"/>
    
    <!--IP地址控件通用样式-->
    <Class name="ip_address" width="140" height="28" bkcolor="white" border_size="1" border_color="light_gray"/>
    
    <!--HotKey控件通用样式-->
    <Class name="hot_key" width="216" height="28" bkcolor="white" border_size="1" border_color="light_gray"/>
    
    <!--HyperLink控件通用样式-->
    <Class name="hyper_link" cursor_type="hand" font="system_underline_14" 
                             normal_text_color="default_link_font_color"
                             hot_text_color="hover_link_font_color" 
                             pushed_text_color="mouse_down_link_font_color"/>
    
    <!--ListCtrl控件列表项通用样式-->
    <!--ListCtrlHeader的样式-->
    <Class name="list_ctrl_header" icon_spacing="4" bkcolor="Cornsilk" width="100%"/>
    <Class name="list_ctrl_header_item" height="100%" text_align="vcenter,hcenter" text_padding="2,0,2,0" hot_color="#FFE5F3FF" pushed_color="#FFD9EEFF"            
                                        show_icon_at_top="true" icon_spacing="6"
                                        sorted_up_image="file='public/listctrl/arrow-sorted-up.svg' width='10' height='10' valign='center' halign='left'" 
                                        sorted_down_image="file='public/listctrl/arrow-sorted-down.svg' width='10' height='10' valign='center' halign='left'"/>
    <Class name="list_ctrl_header_split_box" width="3" height="100%" enable_split_single="true"/>
    <Class name="list_ctrl_header_split_control" width="1" height="100%" margin="2,4,0,2" bkcolor="splitline_level1"/>
    <!--ListCtrl中的CheckBox样式，应用于Header和ListCtrl数据-->
    <Class name="list_ctrl_checkbox" valign="center" halign="left"
                                     normal_image="file='public/checkbox/checkbox-outline-unchecked.svg' padding='2,0,0,0' valign='center'" 
                                     disabled_image="file='public/checkbox/checkbox-outline-unchecked.svg' padding='2,0,0,0' valign='center' fade='80'" 
                                     selected_normal_image="file='public/checkbox/checkbox-outline-checked.svg' padding='2,0,0,0' valign='center'" 
                                     selected_disabled_image="file='public/checkbox/checkbox-outline-checked.svg' padding='2,0,0,0' valign='center' fade='80'" 
                                     part_selected_normal_image="file='public/checkbox/checkbox-outline-mixed.svg' padding='2,0,0,0' valign='center'" 
                                     part_selected_disabled_image="file='public/checkbox/checkbox-outline-mixed.svg' padding='2,0,0,0' valign='center' fade='80'"/>
    <!--ListCtrl中的ReportView的样式-->
    <Class name="list_ctrl_report_view" bkcolor="white" vscrollbar="true" hscrollbar="true" width="100%" padding="0,0,0,0"
                                      child_margin_y="0" paint_selected_colors="true"
                                      frame_selection="true" 
                                      frame_selection_color="#FFAACCEE" frame_selection_alpha="128" 
                                      frame_selection_border_size="1" frame_selection_border_color="#FF0078D7"/>
    <Class name="list_ctrl_item" icon_spacing="4" hot_color="Wheat" pushed_color="#FFD9EEFF" selected_normal_color="#FFCCE8FF"/>
    <Class name="list_ctrl_sub_item" icon_spacing="2" text_align="vcenter,left" text_padding="2,0,2,0" auto_tooltip="true" single_line="true"/>    
    <!--ListCtrl中的IconView的样式-->
    <Class name="list_ctrl_icon_view" bkcolor="white" vscrollbar="true" hscrollbar="true" width="100%" padding="0,0,0,0" paint_selected_colors="true"
                                      horizontal_layout="false" item_size="112,112" columns="auto" child_margin_x="8" child_margin_y="8"
                                      frame_selection="true" 
                                      frame_selection_color="#FFAACCEE" frame_selection_alpha="128" 
                                      frame_selection_border_size="1" frame_selection_border_color="#FF0078D7"/>
    <Class name="list_ctrl_icon_view_item" hot_color="Wheat" pushed_color="#FFD9EEFF" selected_normal_color="#FFCCE8FF"/>
    <Class name="list_ctrl_icon_view_item_image" halign="center" margin="2,2,2,2"/>
    <Class name="list_ctrl_icon_view_item_label" width="100%" height="40" text_padding="2,0,2,0" text_align="hcenter,top" single_line="false" auto_tooltip="true"/>
    <!--ListCtrl中的ListView的样式-->
    <Class name="list_ctrl_list_view" bkcolor="white" vscrollbar="true" hscrollbar="true" height="100%" padding="0,0,0,0" paint_selected_colors="true"
                                      horizontal_layout="false" item_size="240,36" rows="auto" child_margin_x="8" child_margin_y="8"
                                      frame_selection="true" 
                                      frame_selection_color="#FFAACCEE" frame_selection_alpha="128" 
                                      frame_selection_border_size="1" frame_selection_border_color="#FF0078D7"/>
    <Class name="list_ctrl_list_view_item" hot_color="Wheat" pushed_color="#FFD9EEFF" selected_normal_color="#FFCCE8FF"/>
    <Class name="list_ctrl_list_view_item_image" valign="center" margin="8,2,2,2"/>
    <Class name="list_ctrl_list_view_item_label" width="100%" height="100%" text_padding="2,0,2,0" text_align="vcenter,left" single_line="true" auto_tooltip="true"/>
    <!--ListCtrl中的Item编辑框的样式-->
    <Class name="list_ctrl_richedit" text_align="left,vcenter" min_height="28" multi_line="false" word_wrap="true" auto_hscroll="true" 
                                     want_return="true" want_tab="false" rich_text="false" 
                                     text_padding="2,2,2,2" padding="1,1,1,1" border_size="1" 
                                     bkcolor="white" 
                                     border_color="light_gray" 
                                     hot_border_color="blue" 
                                     focus_border_color="CornflowerBlue"/>
    
    <!--TabCtrl控件通用样式-->
    <Class name="tab_ctrl" width="stretch" height="36" border_round="4,4" bkcolor="#FFD3E3FD" vscrollbar="true"/>
    <Class name="tab_ctrl_item" margin="0,4,0,0" height="32"  padding="10,0,0,0"
                                hot_color="#FFA8C7FA" pushed_color="#FFA8C7FA" 
                                selected_normal_color="white" selected_round_corner="12,12" fadehot="false"                                
                                hot_round_corner="5,5" hot_padding="3,0,3,3"
                                auto_hide_close_button="true"
                                icon_class="tab_ctrl_item_icon"
                                title_class="tab_ctrl_item_title"
                                close_button_class="tab_ctrl_item_close"
                                line_class="tab_ctrl_item_line"/>
    <Class name="tab_ctrl_item_icon" width="24" height="24" valign="center" mouse="false"/>
    <Class name="tab_ctrl_item_title" width="stretch" height="24" margin="2,0,24,0" valign="center" font="system_12" auto_tooltip="true" mouse="false"/>
    <Class name="tab_ctrl_item_close" width="16" height="16" margin="0,2,10,8"
                float="true" halign="right" valign="center" visible="false" 
                normal_image="file='public/button/btn_close.svg' height='16' width='16' halign='center' valign='center'"
                hot_color="#FFDCDCDD" pushed_color="#FFC2C2C3" border_round="4,4" alpha="192"/>
    <Class name="tab_ctrl_item_line" bkcolor="#FFA8C7FA" width="1" height="22" float="true" halign="right" valign="center" mouse="false"/>
    
    <!--AddressBar控件通用样式-->
    <Class name="address_bar" width="stretch" height="36" border_round="4,4" bkcolor="white"
                              path_tooltip="true" return_update_ui="true" esc_update_ui="true" kill_focus_update_ui="true"
                              rich_edit_class="address_bar_edit" 
                              rich_edit_clear_btn_class="rich_edit_clear_btn"
                              sub_path_hbox_class="address_bar_sub_path_hbox"
                              sub_path_button_class="address_bar_sub_path_button"
                              sub_path_root_class="address_bar_sub_path_root"
                              path_separator_class="address_bar_path_separator"/>
    <Class name="address_bar_edit" multi_line="false" auto_hscroll="true" rich_text="false" 
                                   default_context_menu="true" select_all_on_focus="true"
                                   want_return="false" want_ctrl_return="false" want_tab="false"
                                   normal_text_color="default_font_color" 
                                   disabled_text_color="disabled_font_color"
                                   text_align="left,vcenter" text_padding="4,2,4,2"/>
    <Class name="address_bar_sub_path_hbox" width="auto" height="100%"/>
    <Class name="address_bar_sub_path_button" width="auto" height="100%" 
                                              text_align="hcenter,vcenter" text_padding="4,2,4,2"
                                              normal_text_color="default_font_color" 
                                              disabled_text_color="disabled_font_color"
                                              hot_color="#FFD7DCE0" 
                                              pushed_color="#FFB0B4B8"/>
    <Class name="address_bar_sub_path_root" padding="2,0,2,0" 
                                            bkimage="file='public/address_bar/folder-root.svg' width='22' height='22' halign='center' valign='center'"/>
    <Class name="address_bar_path_separator" width="auto" height="100%" margin="0,0,0,0"
                                             bkimage="file='public/address_bar/filename-slash.svg' width='22' height='22' halign='center' valign='center'"/>
</Global>