#if 0
//
// Generated by Microsoft (R) HLSL Shader Compiler 10.1
//
//
// Buffer Definitions: 
//
// cbuffer gaussBlurUniform
// {
//
//   int blurRadius;                    // Offset:    0 Size:     4
//   float _texWidth;                   // Offset:    4 Size:     4
//   float _texHeight;                  // Offset:    8 Size:     4
//   float _padding;                    // Offset:   12 Size:     4 [unused]
//   float gaussWeights[514];           // Offset:   16 Size:  8212
//
// }
//
//
// Resource Bindings:
//
// Name                                 Type  Format         Dim      HLSL Bind  Count
// ------------------------------ ---------- ------- ----------- -------------- ------
// sampler_line                      sampler      NA          NA             s0      1 
// inputTex                          texture  float4          2d             t0      1 
// selectAreaTex                     texture  float4          2d             t1      1 
// origTex                           texture  float4          2d             t2      1 
// outTex                                UAV  float4          2d             u0      1 
// gaussBlurUniform                  cbuffer      NA          NA            cb0      1 
//
//
//
// Input signature:
//
// Name                 Index   Mask Register SysValue  Format   Used
// -------------------- ----- ------ -------- -------- ------- ------
// no Input
//
// Output signature:
//
// Name                 Index   Mask Register SysValue  Format   Used
// -------------------- ----- ------ -------- -------- ------- ------
// no Output
cs_5_0
dcl_globalFlags refactoringAllowed
dcl_constantbuffer CB0[515], dynamicIndexed
dcl_sampler s0, mode_default
dcl_resource_texture2d (float,float,float,float) t0
dcl_resource_texture2d (float,float,float,float) t1
dcl_resource_texture2d (float,float,float,float) t2
dcl_uav_typed_texture2d (float,float,float,float) u0
dcl_input vThreadID.xy
dcl_temps 5
dcl_thread_group 32, 32, 1
utof r0.xy, vThreadID.xyxx
add r0.xy, r0.xyxx, l(0.500000, 0.500000, 0.000000, 0.000000)
mul r0.zw, r0.xxxy, cb0[0].yyyz
bfi r1.x, l(31), l(1), cb0[0].x, l(1)
itof r1.y, cb0[0].x
mov r2.x, l(0)
mov r3.xyzw, l(0,0,0,0)
mov r1.z, l(0)
loop 
  ige r2.z, r1.z, r1.x
  breakc_nz r2.z
  add r2.z, -r1.y, cb0[r1.z + 1].x
  mul r2.y, r2.z, cb0[0].z
  mad r2.yz, r0.xxyx, cb0[0].yyzy, r2.xxyx
  sample_l_indexable(texture2d)(float,float,float,float) r4.xyzw, r2.yzyy, t0.xyzw, s0, l(0.000000)
  mul r4.xyz, r4.wwww, r4.xyzx
  iadd r1.zw, r1.zzzz, l(0, 0, 2, 1)
  mad r3.xyzw, r4.xyzw, cb0[r1.w + 1].xxxx, r3.xyzw
endloop 
lt r0.x, l(0.000000), r3.w
div r3.xyz, r3.xyzx, r3.wwww
and r1.xyzw, r3.xyzw, r0.xxxx
sample_l_indexable(texture2d)(float,float,float,float) r0.x, r0.zwzz, t1.zxyw, s0, l(0.000000)
mov r2.xy, vThreadID.xyxx
mov r2.zw, l(0,0,0,0)
ld_indexable(texture2d)(float,float,float,float) r2.xyzw, r2.xyzw, t2.xyzw
add r1.xyzw, r1.xyzw, -r2.xyzw
mad r0.xyzw, r1.xyzw, r0.xxxx, r2.xyzw
store_uav_typed u0.xyzw, vThreadID.xyyy, r0.xyzw
ret 
// Approximately 30 instruction slots used
#endif

const BYTE GaussBlurCSVer[] =
{
     68,  88,  66,  67,  81, 209, 
    149, 194, 158, 120,  61,  28, 
    126, 154,  60, 213,  86, 145, 
     25,  94,   1,   0,   0,   0, 
    240,   7,   0,   0,   5,   0, 
      0,   0,  52,   0,   0,   0, 
     52,   3,   0,   0,  68,   3, 
      0,   0,  84,   3,   0,   0, 
     84,   7,   0,   0,  82,  68, 
     69,  70, 248,   2,   0,   0, 
      1,   0,   0,   0,  64,   1, 
      0,   0,   6,   0,   0,   0, 
     60,   0,   0,   0,   0,   5, 
     83,  67,   0,   1,   0,   0, 
    208,   2,   0,   0,  82,  68, 
     49,  49,  60,   0,   0,   0, 
     24,   0,   0,   0,  32,   0, 
      0,   0,  40,   0,   0,   0, 
     36,   0,   0,   0,  12,   0, 
      0,   0,   0,   0,   0,   0, 
    252,   0,   0,   0,   3,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      1,   0,   0,   0,   1,   0, 
      0,   0,   9,   1,   0,   0, 
      2,   0,   0,   0,   5,   0, 
      0,   0,   4,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0,   1,   0,   0,   0, 
     13,   0,   0,   0,  18,   1, 
      0,   0,   2,   0,   0,   0, 
      5,   0,   0,   0,   4,   0, 
      0,   0, 255, 255, 255, 255, 
      1,   0,   0,   0,   1,   0, 
      0,   0,  13,   0,   0,   0, 
     32,   1,   0,   0,   2,   0, 
      0,   0,   5,   0,   0,   0, 
      4,   0,   0,   0, 255, 255, 
    255, 255,   2,   0,   0,   0, 
      1,   0,   0,   0,  13,   0, 
      0,   0,  40,   1,   0,   0, 
      4,   0,   0,   0,   5,   0, 
      0,   0,   4,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0,   1,   0,   0,   0, 
     13,   0,   0,   0,  47,   1, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   1,   0, 
      0,   0,   1,   0,   0,   0, 
    115,  97, 109, 112, 108, 101, 
    114,  95, 108, 105, 110, 101, 
      0, 105, 110, 112, 117, 116, 
     84, 101, 120,   0, 115, 101, 
    108, 101,  99, 116,  65, 114, 
    101,  97,  84, 101, 120,   0, 
    111, 114, 105, 103,  84, 101, 
    120,   0, 111, 117, 116,  84, 
    101, 120,   0, 103,  97, 117, 
    115, 115,  66, 108, 117, 114, 
     85, 110, 105, 102, 111, 114, 
    109,   0,  47,   1,   0,   0, 
      5,   0,   0,   0,  88,   1, 
      0,   0,  48,  32,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  32,   2,   0,   0, 
      0,   0,   0,   0,   4,   0, 
      0,   0,   2,   0,   0,   0, 
     48,   2,   0,   0,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0, 255, 255, 
    255, 255,   0,   0,   0,   0, 
     84,   2,   0,   0,   4,   0, 
      0,   0,   4,   0,   0,   0, 
      2,   0,   0,   0, 100,   2, 
      0,   0,   0,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0, 136,   2, 
      0,   0,   8,   0,   0,   0, 
      4,   0,   0,   0,   2,   0, 
      0,   0, 100,   2,   0,   0, 
      0,   0,   0,   0, 255, 255, 
    255, 255,   0,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0, 147,   2,   0,   0, 
     12,   0,   0,   0,   4,   0, 
      0,   0,   0,   0,   0,   0, 
    100,   2,   0,   0,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0, 255, 255, 
    255, 255,   0,   0,   0,   0, 
    156,   2,   0,   0,  16,   0, 
      0,   0,  20,  32,   0,   0, 
      2,   0,   0,   0, 172,   2, 
      0,   0,   0,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0,  98, 108, 
    117, 114,  82,  97, 100, 105, 
    117, 115,   0, 105, 110, 116, 
      0, 171,   0,   0,   2,   0, 
      1,   0,   1,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,  43,   2, 
      0,   0,  95, 116, 101, 120, 
     87, 105, 100, 116, 104,   0, 
    102, 108, 111,  97, 116,   0, 
      0,   0,   3,   0,   1,   0, 
      1,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  94,   2,   0,   0, 
     95, 116, 101, 120,  72, 101, 
    105, 103, 104, 116,   0,  95, 
    112,  97, 100, 100, 105, 110, 
    103,   0, 103,  97, 117, 115, 
    115,  87, 101, 105, 103, 104, 
    116, 115,   0, 171, 171, 171, 
      0,   0,   3,   0,   1,   0, 
      1,   0,   2,   2,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  94,   2,   0,   0, 
     77, 105,  99, 114, 111, 115, 
    111, 102, 116,  32,  40,  82, 
     41,  32,  72,  76,  83,  76, 
     32,  83, 104,  97, 100, 101, 
    114,  32,  67, 111, 109, 112, 
    105, 108, 101, 114,  32,  49, 
     48,  46,  49,   0,  73,  83, 
     71,  78,   8,   0,   0,   0, 
      0,   0,   0,   0,   8,   0, 
      0,   0,  79,  83,  71,  78, 
      8,   0,   0,   0,   0,   0, 
      0,   0,   8,   0,   0,   0, 
     83,  72,  69,  88, 248,   3, 
      0,   0,  80,   0,   5,   0, 
    254,   0,   0,   0, 106,   8, 
      0,   1,  89,   8,   0,   4, 
     70, 142,  32,   0,   0,   0, 
      0,   0,   3,   2,   0,   0, 
     90,   0,   0,   3,   0,  96, 
     16,   0,   0,   0,   0,   0, 
     88,  24,   0,   4,   0, 112, 
     16,   0,   0,   0,   0,   0, 
     85,  85,   0,   0,  88,  24, 
      0,   4,   0, 112,  16,   0, 
      1,   0,   0,   0,  85,  85, 
      0,   0,  88,  24,   0,   4, 
      0, 112,  16,   0,   2,   0, 
      0,   0,  85,  85,   0,   0, 
    156,  24,   0,   4,   0, 224, 
     17,   0,   0,   0,   0,   0, 
     85,  85,   0,   0,  95,   0, 
      0,   2,  50,   0,   2,   0, 
    104,   0,   0,   2,   5,   0, 
      0,   0, 155,   0,   0,   4, 
     32,   0,   0,   0,  32,   0, 
      0,   0,   1,   0,   0,   0, 
     86,   0,   0,   4,  50,   0, 
     16,   0,   0,   0,   0,   0, 
     70,   0,   2,   0,   0,   0, 
      0,  10,  50,   0,  16,   0, 
      0,   0,   0,   0,  70,   0, 
     16,   0,   0,   0,   0,   0, 
      2,  64,   0,   0,   0,   0, 
      0,  63,   0,   0,   0,  63, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  56,   0,   0,   8, 
    194,   0,  16,   0,   0,   0, 
      0,   0,   6,   4,  16,   0, 
      0,   0,   0,   0,  86, 137, 
     32,   0,   0,   0,   0,   0, 
      0,   0,   0,   0, 140,   0, 
      0,  12,  18,   0,  16,   0, 
      1,   0,   0,   0,   1,  64, 
      0,   0,  31,   0,   0,   0, 
      1,  64,   0,   0,   1,   0, 
      0,   0,  10, 128,  32,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   1,  64,   0,   0, 
      1,   0,   0,   0,  43,   0, 
      0,   6,  34,   0,  16,   0, 
      1,   0,   0,   0,  10, 128, 
     32,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,  54,   0, 
      0,   5,  18,   0,  16,   0, 
      2,   0,   0,   0,   1,  64, 
      0,   0,   0,   0,   0,   0, 
     54,   0,   0,   8, 242,   0, 
     16,   0,   3,   0,   0,   0, 
      2,  64,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  54,   0,   0,   5, 
     66,   0,  16,   0,   1,   0, 
      0,   0,   1,  64,   0,   0, 
      0,   0,   0,   0,  48,   0, 
      0,   1,  33,   0,   0,   7, 
     66,   0,  16,   0,   2,   0, 
      0,   0,  42,   0,  16,   0, 
      1,   0,   0,   0,  10,   0, 
     16,   0,   1,   0,   0,   0, 
      3,   0,   4,   3,  42,   0, 
     16,   0,   2,   0,   0,   0, 
      0,   0,   0,  11,  66,   0, 
     16,   0,   2,   0,   0,   0, 
     26,   0,  16, 128,  65,   0, 
      0,   0,   1,   0,   0,   0, 
     10, 128,  32,   6,   0,   0, 
      0,   0,   1,   0,   0,   0, 
     42,   0,  16,   0,   1,   0, 
      0,   0,  56,   0,   0,   8, 
     34,   0,  16,   0,   2,   0, 
      0,   0,  42,   0,  16,   0, 
      2,   0,   0,   0,  42, 128, 
     32,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,  50,   0, 
      0,  10,  98,   0,  16,   0, 
      2,   0,   0,   0,   6,   1, 
     16,   0,   0,   0,   0,   0, 
     86, 134,  32,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      6,   1,  16,   0,   2,   0, 
      0,   0,  72,   0,   0, 141, 
    194,   0,   0, 128,  67,  85, 
     21,   0, 242,   0,  16,   0, 
      4,   0,   0,   0, 150,   5, 
     16,   0,   2,   0,   0,   0, 
     70, 126,  16,   0,   0,   0, 
      0,   0,   0,  96,  16,   0, 
      0,   0,   0,   0,   1,  64, 
      0,   0,   0,   0,   0,   0, 
     56,   0,   0,   7, 114,   0, 
     16,   0,   4,   0,   0,   0, 
    246,  15,  16,   0,   4,   0, 
      0,   0,  70,   2,  16,   0, 
      4,   0,   0,   0,  30,   0, 
      0,  10, 194,   0,  16,   0, 
      1,   0,   0,   0, 166,  10, 
     16,   0,   1,   0,   0,   0, 
      2,  64,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      2,   0,   0,   0,   1,   0, 
      0,   0,  50,   0,   0,  12, 
    242,   0,  16,   0,   3,   0, 
      0,   0,  70,  14,  16,   0, 
      4,   0,   0,   0,   6, 128, 
     32,   6,   0,   0,   0,   0, 
      1,   0,   0,   0,  58,   0, 
     16,   0,   1,   0,   0,   0, 
     70,  14,  16,   0,   3,   0, 
      0,   0,  22,   0,   0,   1, 
     49,   0,   0,   7,  18,   0, 
     16,   0,   0,   0,   0,   0, 
      1,  64,   0,   0,   0,   0, 
      0,   0,  58,   0,  16,   0, 
      3,   0,   0,   0,  14,   0, 
      0,   7, 114,   0,  16,   0, 
      3,   0,   0,   0,  70,   2, 
     16,   0,   3,   0,   0,   0, 
    246,  15,  16,   0,   3,   0, 
      0,   0,   1,   0,   0,   7, 
    242,   0,  16,   0,   1,   0, 
      0,   0,  70,  14,  16,   0, 
      3,   0,   0,   0,   6,   0, 
     16,   0,   0,   0,   0,   0, 
     72,   0,   0, 141, 194,   0, 
      0, 128,  67,  85,  21,   0, 
     18,   0,  16,   0,   0,   0, 
      0,   0, 230,  10,  16,   0, 
      0,   0,   0,   0,  38, 125, 
     16,   0,   1,   0,   0,   0, 
      0,  96,  16,   0,   0,   0, 
      0,   0,   1,  64,   0,   0, 
      0,   0,   0,   0,  54,   0, 
      0,   4,  50,   0,  16,   0, 
      2,   0,   0,   0,  70,   0, 
      2,   0,  54,   0,   0,   8, 
    194,   0,  16,   0,   2,   0, 
      0,   0,   2,  64,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,  45,   0, 
      0, 137, 194,   0,   0, 128, 
     67,  85,  21,   0, 242,   0, 
     16,   0,   2,   0,   0,   0, 
     70,  14,  16,   0,   2,   0, 
      0,   0,  70, 126,  16,   0, 
      2,   0,   0,   0,   0,   0, 
      0,   8, 242,   0,  16,   0, 
      1,   0,   0,   0,  70,  14, 
     16,   0,   1,   0,   0,   0, 
     70,  14,  16, 128,  65,   0, 
      0,   0,   2,   0,   0,   0, 
     50,   0,   0,   9, 242,   0, 
     16,   0,   0,   0,   0,   0, 
     70,  14,  16,   0,   1,   0, 
      0,   0,   6,   0,  16,   0, 
      0,   0,   0,   0,  70,  14, 
     16,   0,   2,   0,   0,   0, 
    164,   0,   0,   6, 242, 224, 
     17,   0,   0,   0,   0,   0, 
     70,   5,   2,   0,  70,  14, 
     16,   0,   0,   0,   0,   0, 
     62,   0,   0,   1,  83,  84, 
     65,  84, 148,   0,   0,   0, 
     30,   0,   0,   0,   5,   0, 
      0,   0,   0,   0,   0,   0, 
      1,   0,   0,   0,  11,   0, 
      0,   0,   2,   0,   0,   0, 
      1,   0,   0,   0,   1,   0, 
      0,   0,   1,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      3,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   5,   0, 
      0,   0,   0,   0,   0,   0, 
      2,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      1,   0,   0,   0
};
