<?xml version="1.0" encoding="UTF-8"?>
<!-- 定义图片的宽度和高度：添加preserveAspectRatio确保缩放时比例正确  -->
<svg xmlns="http://www.w3.org/2000/svg"
     viewBox="0 0 264 266" width="264" height="266" preserveAspectRatio="xMidYMid meet">
  <!-- 定义阴影滤镜（更自然的参数） -->
  <defs>
    <filter id="naturalShadow" x="-14%" y="-14%" width="128%" height="128%">
      <!-- dx:水平偏移，dy:垂直偏移（模拟光源从左上角照射） -->
      <feOffset dx="4" dy="6" in="SourceAlpha" result="offset"/>
      <!-- 适度的模糊效果：stdDeviation为模糊半径 -->
      <feGaussianBlur in="offset" stdDeviation="10" result="blur"/>
      <!-- 带透明度的阴影：第二行最后一个数字的是透明度，0.4代表透明度40% -->
      <feColorMatrix in="blur" type="matrix" 
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
    </filter>
  </defs>

  <!-- 带阴影的主体矩形（使用x/y定位更直观），rx代表圆角半径，ry可以省略，默认与rx相同 -->
  <rect x="30" y="30" width="200" height="200" rx="6"
        fill="#FFFF00" filter="url(#naturalShadow)"/>
  
  <!-- 描边层（与主体分离，避免阴影干扰描边） -->
  <rect x="30" y="30" width="200" height="200" rx="6"
        fill="white" stroke="gray" stroke-width="1.6"/>
</svg>

<!-- 图片尺寸的计算方法 -->
<!-- 总宽度 = 原始宽度 + 水平阴影扩展 -->
<!-- 总高度 = 原始高度 + 垂直阴影扩展 -->

<!-- 水平阴影扩展 = 左侧扩展 + 右侧扩展 -->
<!-- 垂直阴影扩展 = 上方扩展 + 下方扩展 -->

<!-- 左侧扩展：[3 * stdDeviation = 30 (模糊)] = 30px -->
<!-- 右侧扩展：[3 * stdDeviation = 30 (模糊)]  + 4 (偏移) = 34px -->

<!-- 上方扩展：[3 * stdDeviation = 30 (模糊)] = 30px -->
<!-- 下方扩展：[3 * stdDeviation = 30 (模糊)] + 6 (偏移) = 36px -->

<!-- 总宽度 = 200 + 30 + 34 = 264 -->
<!-- 总高度 = 200 + 30 + 36 = 266 -->

<!-- corner='30,30,34,36' -->
