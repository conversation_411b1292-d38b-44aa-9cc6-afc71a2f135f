<?xml version="1.0" encoding="UTF-8"?>
<Window>
    <Class name="color_combo_picker_btn" font="system_14" normal_text_color="black" disabled_text_color="gray" text_align="hcenter,vcenter" border_size="1" hot_border_color="#FFB3D0EE" pushed_border_color="#FF82B4E8" hot_color="#FFE8EFF7" pushed_color="#FFC9E0F7" />
    <!-- 第一个节点会被跳过解析 -->
    <Box>
        <VBox bkcolor="bk_wnd_darkcolor">
            <!-- 常用颜色 -->
            <ColorPickerRegular name="color_combo_picker" color_type="default" item_size="40,20" columns="10" child_margin="2" padding="2,2,2,2" halign="center" valign="center"/>
            <Button class="color_combo_picker_btn" name="color_combo_picker_more" text="  更多颜色 ...  " width="auto" height="30" margin="2,0,2,2" halign="center"/>
        </VBox>
    </Box>
</Window>
