#if 0
//
// Generated by Microsoft (R) HLSL Shader Compiler 10.1
//
//
// Buffer Definitions: 
//
// cbuffer gaussBlurUniform
// {
//
//   int blurRadius;                    // Offset:    0 Size:     4
//   float _texWidth;                   // Offset:    4 Size:     4
//   float _texHeight;                  // Offset:    8 Size:     4
//   float _padding;                    // Offset:   12 Size:     4 [unused]
//   float gaussWeights[514];           // Offset:   16 Size:  8212
//
// }
//
//
// Resource Bindings:
//
// Name                                 Type  Format         Dim      HLSL Bind  Count
// ------------------------------ ---------- ------- ----------- -------------- ------
// sampler_line                      sampler      NA          NA             s0      1 
// origTex                           texture  float4          2d             t0      1 
// midTex                                UAV  float4          2d             u0      1 
// gaussBlurUniform                  cbuffer      NA          NA            cb0      1 
//
//
//
// Input signature:
//
// Name                 Index   Mask Register SysValue  Format   Used
// -------------------- ----- ------ -------- -------- ------- ------
// no Input
//
// Output signature:
//
// Name                 Index   Mask Register SysValue  Format   Used
// -------------------- ----- ------ -------- -------- ------- ------
// no Output
cs_5_0
dcl_globalFlags refactoringAllowed
dcl_constantbuffer CB0[515], dynamicIndexed
dcl_sampler s0, mode_default
dcl_resource_texture2d (float,float,float,float) t0
dcl_uav_typed_texture2d (float,float,float,float) u0
dcl_input vThreadID.xy
dcl_temps 4
dcl_thread_group 32, 32, 1
bfi r0.x, l(31), l(1), cb0[0].x, l(1)
itof r0.y, cb0[0].x
utof r0.zw, vThreadID.xxxy
add r0.zw, r0.zzzw, l(0.000000, 0.000000, 0.500000, 0.500000)
mov r1.y, l(0)
mov r2.xyzw, l(0,0,0,0)
mov r1.z, l(0)
loop 
  ige r3.x, r1.z, r0.x
  breakc_nz r3.x
  add r3.x, -r0.y, cb0[r1.z + 1].x
  mul r1.x, r3.x, cb0[0].y
  mad r3.xy, r0.zwzz, cb0[0].yzyy, r1.xyxx
  sample_l_indexable(texture2d)(float,float,float,float) r3.xyzw, r3.xyxx, t0.xyzw, s0, l(0.000000)
  mul r3.xyz, r3.wwww, r3.xyzx
  iadd r1.zw, r1.zzzz, l(0, 0, 2, 1)
  mad r2.xyzw, r3.xyzw, cb0[r1.w + 1].xxxx, r2.xyzw
endloop 
lt r0.x, l(0.000000), r2.w
div r2.xyz, r2.xyzx, r2.wwww
and r0.xyzw, r2.xyzw, r0.xxxx
store_uav_typed u0.xyzw, vThreadID.xyyy, r0.xyzw
ret 
// Approximately 23 instruction slots used
#endif

const BYTE GaussBlurCSHor[] =
{
     68,  88,  66,  67, 122, 129, 
    118,  90,  37,  15, 130, 117, 
     24, 210,  21,  29, 126,  56, 
     56, 110,   1,   0,   0,   0, 
    144,   6,   0,   0,   5,   0, 
      0,   0,  52,   0,   0,   0, 
    224,   2,   0,   0, 240,   2, 
      0,   0,   0,   3,   0,   0, 
    244,   5,   0,   0,  82,  68, 
     69,  70, 164,   2,   0,   0, 
      1,   0,   0,   0, 236,   0, 
      0,   0,   4,   0,   0,   0, 
     60,   0,   0,   0,   0,   5, 
     83,  67,   0,   1,   0,   0, 
    124,   2,   0,   0,  82,  68, 
     49,  49,  60,   0,   0,   0, 
     24,   0,   0,   0,  32,   0, 
      0,   0,  40,   0,   0,   0, 
     36,   0,   0,   0,  12,   0, 
      0,   0,   0,   0,   0,   0, 
    188,   0,   0,   0,   3,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      1,   0,   0,   0,   1,   0, 
      0,   0, 201,   0,   0,   0, 
      2,   0,   0,   0,   5,   0, 
      0,   0,   4,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0,   1,   0,   0,   0, 
     13,   0,   0,   0, 209,   0, 
      0,   0,   4,   0,   0,   0, 
      5,   0,   0,   0,   4,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0,   1,   0, 
      0,   0,  13,   0,   0,   0, 
    216,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      1,   0,   0,   0,   1,   0, 
      0,   0, 115,  97, 109, 112, 
    108, 101, 114,  95, 108, 105, 
    110, 101,   0, 111, 114, 105, 
    103,  84, 101, 120,   0, 109, 
    105, 100,  84, 101, 120,   0, 
    103,  97, 117, 115, 115,  66, 
    108, 117, 114,  85, 110, 105, 
    102, 111, 114, 109,   0, 171, 
    171, 171, 216,   0,   0,   0, 
      5,   0,   0,   0,   4,   1, 
      0,   0,  48,  32,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0, 204,   1,   0,   0, 
      0,   0,   0,   0,   4,   0, 
      0,   0,   2,   0,   0,   0, 
    220,   1,   0,   0,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0, 255, 255, 
    255, 255,   0,   0,   0,   0, 
      0,   2,   0,   0,   4,   0, 
      0,   0,   4,   0,   0,   0, 
      2,   0,   0,   0,  16,   2, 
      0,   0,   0,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0,  52,   2, 
      0,   0,   8,   0,   0,   0, 
      4,   0,   0,   0,   2,   0, 
      0,   0,  16,   2,   0,   0, 
      0,   0,   0,   0, 255, 255, 
    255, 255,   0,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0,  63,   2,   0,   0, 
     12,   0,   0,   0,   4,   0, 
      0,   0,   0,   0,   0,   0, 
     16,   2,   0,   0,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0, 255, 255, 
    255, 255,   0,   0,   0,   0, 
     72,   2,   0,   0,  16,   0, 
      0,   0,  20,  32,   0,   0, 
      2,   0,   0,   0,  88,   2, 
      0,   0,   0,   0,   0,   0, 
    255, 255, 255, 255,   0,   0, 
      0,   0, 255, 255, 255, 255, 
      0,   0,   0,   0,  98, 108, 
    117, 114,  82,  97, 100, 105, 
    117, 115,   0, 105, 110, 116, 
      0, 171,   0,   0,   2,   0, 
      1,   0,   1,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0, 215,   1, 
      0,   0,  95, 116, 101, 120, 
     87, 105, 100, 116, 104,   0, 
    102, 108, 111,  97, 116,   0, 
      0,   0,   3,   0,   1,   0, 
      1,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  10,   2,   0,   0, 
     95, 116, 101, 120,  72, 101, 
    105, 103, 104, 116,   0,  95, 
    112,  97, 100, 100, 105, 110, 
    103,   0, 103,  97, 117, 115, 
    115,  87, 101, 105, 103, 104, 
    116, 115,   0, 171, 171, 171, 
      0,   0,   3,   0,   1,   0, 
      1,   0,   2,   2,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  10,   2,   0,   0, 
     77, 105,  99, 114, 111, 115, 
    111, 102, 116,  32,  40,  82, 
     41,  32,  72,  76,  83,  76, 
     32,  83, 104,  97, 100, 101, 
    114,  32,  67, 111, 109, 112, 
    105, 108, 101, 114,  32,  49, 
     48,  46,  49,   0,  73,  83, 
     71,  78,   8,   0,   0,   0, 
      0,   0,   0,   0,   8,   0, 
      0,   0,  79,  83,  71,  78, 
      8,   0,   0,   0,   0,   0, 
      0,   0,   8,   0,   0,   0, 
     83,  72,  69,  88, 236,   2, 
      0,   0,  80,   0,   5,   0, 
    187,   0,   0,   0, 106,   8, 
      0,   1,  89,   8,   0,   4, 
     70, 142,  32,   0,   0,   0, 
      0,   0,   3,   2,   0,   0, 
     90,   0,   0,   3,   0,  96, 
     16,   0,   0,   0,   0,   0, 
     88,  24,   0,   4,   0, 112, 
     16,   0,   0,   0,   0,   0, 
     85,  85,   0,   0, 156,  24, 
      0,   4,   0, 224,  17,   0, 
      0,   0,   0,   0,  85,  85, 
      0,   0,  95,   0,   0,   2, 
     50,   0,   2,   0, 104,   0, 
      0,   2,   4,   0,   0,   0, 
    155,   0,   0,   4,  32,   0, 
      0,   0,  32,   0,   0,   0, 
      1,   0,   0,   0, 140,   0, 
      0,  12,  18,   0,  16,   0, 
      0,   0,   0,   0,   1,  64, 
      0,   0,  31,   0,   0,   0, 
      1,  64,   0,   0,   1,   0, 
      0,   0,  10, 128,  32,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   1,  64,   0,   0, 
      1,   0,   0,   0,  43,   0, 
      0,   6,  34,   0,  16,   0, 
      0,   0,   0,   0,  10, 128, 
     32,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,  86,   0, 
      0,   4, 194,   0,  16,   0, 
      0,   0,   0,   0,   6,   4, 
      2,   0,   0,   0,   0,  10, 
    194,   0,  16,   0,   0,   0, 
      0,   0, 166,  14,  16,   0, 
      0,   0,   0,   0,   2,  64, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,  63,   0,   0,   0,  63, 
     54,   0,   0,   5,  34,   0, 
     16,   0,   1,   0,   0,   0, 
      1,  64,   0,   0,   0,   0, 
      0,   0,  54,   0,   0,   8, 
    242,   0,  16,   0,   2,   0, 
      0,   0,   2,  64,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,  54,   0, 
      0,   5,  66,   0,  16,   0, 
      1,   0,   0,   0,   1,  64, 
      0,   0,   0,   0,   0,   0, 
     48,   0,   0,   1,  33,   0, 
      0,   7,  18,   0,  16,   0, 
      3,   0,   0,   0,  42,   0, 
     16,   0,   1,   0,   0,   0, 
     10,   0,  16,   0,   0,   0, 
      0,   0,   3,   0,   4,   3, 
     10,   0,  16,   0,   3,   0, 
      0,   0,   0,   0,   0,  11, 
     18,   0,  16,   0,   3,   0, 
      0,   0,  26,   0,  16, 128, 
     65,   0,   0,   0,   0,   0, 
      0,   0,  10, 128,  32,   6, 
      0,   0,   0,   0,   1,   0, 
      0,   0,  42,   0,  16,   0, 
      1,   0,   0,   0,  56,   0, 
      0,   8,  18,   0,  16,   0, 
      1,   0,   0,   0,  10,   0, 
     16,   0,   3,   0,   0,   0, 
     26, 128,  32,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
     50,   0,   0,  10,  50,   0, 
     16,   0,   3,   0,   0,   0, 
    230,  10,  16,   0,   0,   0, 
      0,   0, 150, 133,  32,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,  70,   0,  16,   0, 
      1,   0,   0,   0,  72,   0, 
      0, 141, 194,   0,   0, 128, 
     67,  85,  21,   0, 242,   0, 
     16,   0,   3,   0,   0,   0, 
     70,   0,  16,   0,   3,   0, 
      0,   0,  70, 126,  16,   0, 
      0,   0,   0,   0,   0,  96, 
     16,   0,   0,   0,   0,   0, 
      1,  64,   0,   0,   0,   0, 
      0,   0,  56,   0,   0,   7, 
    114,   0,  16,   0,   3,   0, 
      0,   0, 246,  15,  16,   0, 
      3,   0,   0,   0,  70,   2, 
     16,   0,   3,   0,   0,   0, 
     30,   0,   0,  10, 194,   0, 
     16,   0,   1,   0,   0,   0, 
    166,  10,  16,   0,   1,   0, 
      0,   0,   2,  64,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   2,   0,   0,   0, 
      1,   0,   0,   0,  50,   0, 
      0,  12, 242,   0,  16,   0, 
      2,   0,   0,   0,  70,  14, 
     16,   0,   3,   0,   0,   0, 
      6, 128,  32,   6,   0,   0, 
      0,   0,   1,   0,   0,   0, 
     58,   0,  16,   0,   1,   0, 
      0,   0,  70,  14,  16,   0, 
      2,   0,   0,   0,  22,   0, 
      0,   1,  49,   0,   0,   7, 
     18,   0,  16,   0,   0,   0, 
      0,   0,   1,  64,   0,   0, 
      0,   0,   0,   0,  58,   0, 
     16,   0,   2,   0,   0,   0, 
     14,   0,   0,   7, 114,   0, 
     16,   0,   2,   0,   0,   0, 
     70,   2,  16,   0,   2,   0, 
      0,   0, 246,  15,  16,   0, 
      2,   0,   0,   0,   1,   0, 
      0,   7, 242,   0,  16,   0, 
      0,   0,   0,   0,  70,  14, 
     16,   0,   2,   0,   0,   0, 
      6,   0,  16,   0,   0,   0, 
      0,   0, 164,   0,   0,   6, 
    242, 224,  17,   0,   0,   0, 
      0,   0,  70,   5,   2,   0, 
     70,  14,  16,   0,   0,   0, 
      0,   0,  62,   0,   0,   1, 
     83,  84,  65,  84, 148,   0, 
      0,   0,  23,   0,   0,   0, 
      4,   0,   0,   0,   0,   0, 
      0,   0,   1,   0,   0,   0, 
      8,   0,   0,   0,   2,   0, 
      0,   0,   1,   0,   0,   0, 
      1,   0,   0,   0,   1,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   1,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      3,   0,   0,   0,   0,   0, 
      0,   0,   2,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   0,   0,   0,   0, 
      0,   0,   1,   0,   0,   0
};
