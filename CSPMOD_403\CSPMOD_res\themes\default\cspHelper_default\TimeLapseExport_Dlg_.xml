<?xml version="1.0" encoding="UTF-8"?>
<Window size="360,180" min_size="400,240" 
        caption="0,0,0,28" use_system_caption="false" snap_layout_menu="true" sys_menu="true" sys_menu_rect="0,0,36,36" 
        shadow_type="default" shadow_attached="true" shadow_snap="true" layered_window="true" 
        alpha="255" size_box="4,4,4,4" icon="public/caption/logo.ico">
    <VBox bkcolor="#CSPColor_PanelBG" visible="true">    
        <!-- 标题栏区域 -->
        <HBox name="window_caption_bar" width="stretch" height="28" normal_color="#CSPColor_CaptionFocus" disabled_color="#CSPColor_CaptionNoFocus">
			<Label name="text_Title" text="缩时摄影导出"  text_align="vcenter"  margin="8,0,8,0" valign="center"  normal_text_color="#CSPColor_CaptionContent" disabled_text_color="CSPColor_CaptionContentNoFocus" font="system_14"/>
            <Control />

            <!--<Button class="btn_wnd_close_11" height="stretch" width="40" name="closebtn" margin="0,0,0,2" tooltip_text="关闭"/>-->
            <Button class="btn_wnd_close_11" height="stretch" width="40" name="btn_Close" margin="0,0,0,2" tooltip_text="关闭" normal_image="file='icon_close.svg' width='24' height='24' valign='center' halign='center'"/>
        </HBox>
        
        <!-- 工作区域，除了标题栏外的内容都放在这个大的Box区域 -->

		<VBox>
			<Control />
			<VBox margin="8,0,16,0" valign="center" halign="center" height="auto">
				<HBox height="auto" margin="0,8,0,8">
					<Label text="视频大小" valign="center" margin="6,0,6,0" text_align="vcenter"  normal_text_color="#CSPColor_Content" font="system_14"/>
					<RichEdit class="simple prompt simple_border_bottom" name="edit_VideoSize" width="140" height="30" margin="0,3" font="system_14"
			  text_align="vcenter" text_padding="8,4,8,4" number_only="true" normal_text_color="#CSPColor_Content" bkcolor="#CSPColor_ControlBG" 
			  prompt_text="edit_VideoSize" />
					<Button margin="8,0,8,0" name="btn_SetAsCanvasSize" text="画布大小"  valign="center" width="90" height="30" border_size="1" border_round="2,2" border_color="#CSPColor_Separator"
		normal_text_color="#CSPColor_Content" bkcolor="#CSPColor_ControlBG" hot_color="#CSPColor_Selected" pushed_color="#CSPColor_ButtonPush"/>

				</HBox>
				<HBox height="auto"  margin="0,8,0,8">
					<Label text="视频时长" valign="center" margin="6,0,6,0"  text_align="vcenter"  normal_text_color="#CSPColor_Content" font="system_14"/>
				<RichEdit class="simple prompt simple_border_bottom"  name="edit_VideoDuration" width="140" height="30" margin="0,3" font="system_14" enabled="true"
		  text_align="vcenter" text_padding="8,4,8,4"  number_only="true" normal_text_color="#CSPColor_Content"  bkcolor="#CSPColor_ControlBG" 
		  prompt_text="edit_VideoDuration" />
				</HBox>
			</VBox>
			<Control />

			<!--按钮区-->
			<HBox margin="8,8,8,8"  halign="center" height="auto">
				<Control />
				<Button margin="4,0,4,0" name="btn_OK" text="确定" width="90" height="30" border_size="1" border_round="2,2" border_color="#CSPColor_Separator"
						normal_text_color="#CSPColor_Content" bkcolor="#CSPColor_BlueButton" hot_color="#CSPColor_BlueButtonHot" pushed_color="#CSPColor_BlueButtonPush"/>
				<Button margin="4,0,4,0" name="btn_Cancel" text="取消" width="90" height="30" border_size="1" border_round="2,2" border_color="#CSPColor_Separator"
						normal_text_color="#CSPColor_Content" bkcolor="#CSPColor_ControlBG" hot_color="#CSPColor_Selected" pushed_color="#CSPColor_ButtonPush"/>
				
				
			</HBox>
			
		</VBox>
		
		

    </VBox>
</Window>